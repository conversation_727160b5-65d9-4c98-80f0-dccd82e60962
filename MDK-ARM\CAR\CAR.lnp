--cpu Cortex-M3
"car\startup_stm32f103xb.o"
"car\main.o"
"car\gpio.o"
"car\dma.o"
"car\i2c.o"
"car\tim.o"
"car\usart.o"
"car\stm32f1xx_it.o"
"car\stm32f1xx_hal_msp.o"
"car\stm32f1xx_hal_gpio_ex.o"
"car\stm32f1xx_hal_i2c.o"
"car\stm32f1xx_hal.o"
"car\stm32f1xx_hal_rcc.o"
"car\stm32f1xx_hal_rcc_ex.o"
"car\stm32f1xx_hal_gpio.o"
"car\stm32f1xx_hal_dma.o"
"car\stm32f1xx_hal_cortex.o"
"car\stm32f1xx_hal_pwr.o"
"car\stm32f1xx_hal_flash.o"
"car\stm32f1xx_hal_flash_ex.o"
"car\stm32f1xx_hal_exti.o"
"car\stm32f1xx_hal_tim.o"
"car\stm32f1xx_hal_tim_ex.o"
"car\stm32f1xx_hal_uart.o"
"car\system_stm32f1xx.o"
"car\oled.o"
"car\oled_font.o"
"car\iic.o"
"car\inv_mpu.o"
"car\inv_mpu_dmp_motion_driver.o"
"car\mpu6050.o"
"car\mpu6050_driver.o"
"car\motor_driver.o"
"car\oled_app.o"
"car\mpu6050_app.o"
"car\uart_app.o"
"car\motor_app.o"
"car\scheduler.o"
--strict --scatter "CAR\CAR.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "CAR.map" -o CAR\CAR.axf