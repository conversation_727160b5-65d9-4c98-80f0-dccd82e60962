#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART3_RX
Dma.RequestsNb=1
Dma.USART3_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.0.Instance=DMA1_Channel3
Dma.USART3_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.0.Mode=DMA_NORMAL
Dma.USART3_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
File.Version=6
GPIO.groupedBy=
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=TIM1
Mcu.IP6=USART3
Mcu.IPNb=7
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PD0-OSC_IN
Mcu.Pin1=PD1-OSC_OUT
Mcu.Pin10=PA11
Mcu.Pin11=PA13
Mcu.Pin12=PA14
Mcu.Pin13=PB3
Mcu.Pin14=PB4
Mcu.Pin15=PB8
Mcu.Pin16=PB9
Mcu.Pin17=VP_SYS_VS_Systick
Mcu.Pin18=VP_TIM1_VS_ClockSourceINT
Mcu.Pin2=PB0
Mcu.Pin3=PB10
Mcu.Pin4=PB11
Mcu.Pin5=PB12
Mcu.Pin6=PB13
Mcu.Pin7=PB14
Mcu.Pin8=PB15
Mcu.Pin9=PA8
Mcu.PinsNb=19
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.8.1
MxDb.Version=DB.6.0.81
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Channel3_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA11.Locked=true
PA11.Signal=S_TIM1_CH4
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA8.Locked=true
PA8.Signal=S_TIM1_CH1
PB0.Locked=true
PB0.Signal=GPIO_Output
PB10.Locked=true
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Locked=true
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.Locked=true
PB15.Signal=GPIO_Output
PB3.Locked=true
PB3.Signal=GPIO_Output
PB4.Locked=true
PB4.Signal=GPIO_Output
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=D\:\\ProgramFile\\STM32CubeMX\\Packs\\STM32Cube_FW_F1_V1.8.4
ProjectManager.DefaultFWLocation=false
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=CAR.ioc
ProjectManager.ProjectName=CAR
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.27
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_USART3_UART_Init-USART3-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation4 CH4,Prescaler,Period
TIM1.Period=1000-1
TIM1.Prescaler=72
USART3.BaudRate=460800
USART3.IPParameters=VirtualMode,BaudRate
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
board=custom
