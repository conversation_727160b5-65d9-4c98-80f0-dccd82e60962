#ifndef __BSP_H
#define __BSP_H

#include "string.h"
#include "stdint.h"
#include "stdio.h"
#include "stdarg.h"

#include "mpu6050_driver.h"
#include "motor_driver.h"


#include "oled.h"
#include "inv_mpu.h"
#include "inv_mpu_dmp_motion_driver.h"
#include "mpu6050.h"



#include "main.h"
#include "scheduler.h"
#include "oled_app.h"
#include "mpu6050_app.h"
#include "uart_app.h"
#include "motor_app.h"


extern UART_HandleTypeDef huart3;
extern uint8_t uart_rx_dma_buffer[128];
extern Motor_t left_motor;
extern Motor_t right_motor;

#endif

