#include "motor_app.h"
#include "tim.h"


Motor_t left_motor;
Motor_t right_motor;

void motor_init(void)
{
    TB6612_Init(1);
    Motor_Create(&left_motor, &htim1, TIM_CHANNEL_4, 
             GPIOB, GPIO_PIN_13,              
             GPIOB, GPIO_PIN_12,             
             0);                           
    Motor_Create(&right_motor, &htim1, TIM_CHANNEL_1,
             GPIOB, GPIO_PIN_14,            
             GPIOB, GPIO_PIN_15,             
             1);                             
}

void motor_proc(void)
{
    
}

