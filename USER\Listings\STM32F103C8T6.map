Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.main) refers to med_mcu.o(i.Med_Mcu_Iint) for Med_Mcu_Iint
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    exit.o(i.EXTI9_5_IRQHandler) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    exit.o(i.EXTI9_5_IRQHandler) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    exit.o(i.EXTI9_5_IRQHandler) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    exit.o(i.EXTI9_5_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    exit.o(i.EXTI9_5_IRQHandler) refers to med_mpu6050.o(i.Med_Mpu6050_GetAngles) for Med_Mpu6050_GetAngles
    exit.o(i.EXTI9_5_IRQHandler) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    exit.o(i.EXTI9_5_IRQHandler) refers to noretval__2printf.o(.text) for __2printf
    exit.o(i.EXTI9_5_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    exit.o(i.Exit_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    exit.o(i.Exit_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    exit.o(i.Exit_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    exit.o(i.Exit_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to exit.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    drv_mpu6050.o(i.Drv_Mpu6050_Gpio_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drv_mpu6050.o(i.Drv_Mpu6050_Gpio_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    drv_mpu6050.o(i.Drv_Mpu6050_Gpio_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    med_mcu.o(i.Med_Mcu_Iint) refers to delay.o(i.delay_init) for delay_init
    med_mcu.o(i.Med_Mcu_Iint) refers to usart.o(i.uart_init) for uart_init
    med_mcu.o(i.Med_Mcu_Iint) refers to exit.o(i.Exit_Init) for Exit_Init
    med_mcu.o(i.Med_Mcu_Iint) refers to med_mpu6050.o(i.Med_Mpu6050_Init) for Med_Mpu6050_Init
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to med_mpu6050.o(i.Med_Mpu6050_ReadReg) for Med_Mpu6050_ReadReg
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to atan2.o(i.atan2) for atan2
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to delay.o(i.delay_ms) for delay_ms
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) refers to filter.o(.data) for gZeroAngleX
    med_mpu6050.o(i.MedMpu6050_SendOneByte) refers to delay.o(i.delay_us) for delay_us
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to med_mpu6050.o(i.MPU6050_CalibrateZeroPosition) for MPU6050_CalibrateZeroPosition
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to med_mpu6050.o(i.Med_Mpu6050_ReadReg) for Med_Mpu6050_ReadReg
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to atan2.o(i.atan2) for atan2
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to filter.o(i.Kalman_Filter_Angle) for Kalman_Filter_Angle
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to med_mpu6050.o(.data) for firstRun
    med_mpu6050.o(i.Med_Mpu6050_GetAngles) refers to filter.o(.data) for kfX
    med_mpu6050.o(i.Med_Mpu6050_Iic_SendAck) refers to delay.o(i.delay_us) for delay_us
    med_mpu6050.o(i.Med_Mpu6050_Iic_SendNAck) refers to delay.o(i.delay_us) for delay_us
    med_mpu6050.o(i.Med_Mpu6050_Iic_Start) refers to delay.o(i.delay_us) for delay_us
    med_mpu6050.o(i.Med_Mpu6050_Iic_Stop) refers to delay.o(i.delay_us) for delay_us
    med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck) refers to delay.o(i.delay_us) for delay_us
    med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Stop) for Med_Mpu6050_Iic_Stop
    med_mpu6050.o(i.Med_Mpu6050_Init) refers to drv_mpu6050.o(i.Drv_Mpu6050_Gpio_Init) for Drv_Mpu6050_Gpio_Init
    med_mpu6050.o(i.Med_Mpu6050_Init) refers to med_mpu6050.o(i.Med_Mpu6050_WriteReg) for Med_Mpu6050_WriteReg
    med_mpu6050.o(i.Med_Mpu6050_Init) refers to delay.o(i.delay_ms) for delay_ms
    med_mpu6050.o(i.Med_Mpu6050_Init) refers to med_mpu6050.o(i.Med_Mpu6050_ReadReg) for Med_Mpu6050_ReadReg
    med_mpu6050.o(i.Med_Mpu6050_ReadData) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Start) for Med_Mpu6050_Iic_Start
    med_mpu6050.o(i.Med_Mpu6050_ReadData) refers to med_mpu6050.o(i.MedMpu6050_SendOneByte) for MedMpu6050_SendOneByte
    med_mpu6050.o(i.Med_Mpu6050_ReadData) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck) for Med_Mpu6050_Iic_WaitAck
    med_mpu6050.o(i.Med_Mpu6050_ReadData) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Stop) for Med_Mpu6050_Iic_Stop
    med_mpu6050.o(i.Med_Mpu6050_ReadData) refers to med_mpu6050.o(i.Med_Mpu6050_ReadOneByte) for Med_Mpu6050_ReadOneByte
    med_mpu6050.o(i.Med_Mpu6050_ReadOneByte) refers to delay.o(i.delay_us) for delay_us
    med_mpu6050.o(i.Med_Mpu6050_ReadOneByte) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_SendNAck) for Med_Mpu6050_Iic_SendNAck
    med_mpu6050.o(i.Med_Mpu6050_ReadOneByte) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_SendAck) for Med_Mpu6050_Iic_SendAck
    med_mpu6050.o(i.Med_Mpu6050_ReadReg) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Start) for Med_Mpu6050_Iic_Start
    med_mpu6050.o(i.Med_Mpu6050_ReadReg) refers to med_mpu6050.o(i.MedMpu6050_SendOneByte) for MedMpu6050_SendOneByte
    med_mpu6050.o(i.Med_Mpu6050_ReadReg) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck) for Med_Mpu6050_Iic_WaitAck
    med_mpu6050.o(i.Med_Mpu6050_ReadReg) refers to med_mpu6050.o(i.Med_Mpu6050_ReadOneByte) for Med_Mpu6050_ReadOneByte
    med_mpu6050.o(i.Med_Mpu6050_ReadReg) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Stop) for Med_Mpu6050_Iic_Stop
    med_mpu6050.o(i.Med_Mpu6050_WriteData) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Start) for Med_Mpu6050_Iic_Start
    med_mpu6050.o(i.Med_Mpu6050_WriteData) refers to med_mpu6050.o(i.MedMpu6050_SendOneByte) for MedMpu6050_SendOneByte
    med_mpu6050.o(i.Med_Mpu6050_WriteData) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck) for Med_Mpu6050_Iic_WaitAck
    med_mpu6050.o(i.Med_Mpu6050_WriteData) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Stop) for Med_Mpu6050_Iic_Stop
    med_mpu6050.o(i.Med_Mpu6050_WriteReg) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Start) for Med_Mpu6050_Iic_Start
    med_mpu6050.o(i.Med_Mpu6050_WriteReg) refers to med_mpu6050.o(i.MedMpu6050_SendOneByte) for MedMpu6050_SendOneByte
    med_mpu6050.o(i.Med_Mpu6050_WriteReg) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck) for Med_Mpu6050_Iic_WaitAck
    med_mpu6050.o(i.Med_Mpu6050_WriteReg) refers to med_mpu6050.o(i.Med_Mpu6050_Iic_Stop) for Med_Mpu6050_Iic_Stop
    filter.o(i.Kalman_Filter_Angle) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    filter.o(i.Kalman_Filter_Angle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    filter.o(i.Kalman_Filter_Angle) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    filter.o(i.Kalman_Filter_Angle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    filter.o(i.Kalman_Filter_Angle) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing med_mpu6050.o(i.Med_Mpu6050_ReadData), (116 bytes).
    Removing med_mpu6050.o(i.Med_Mpu6050_WriteData), (96 bytes).

468 unused section(s) (total 18420 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\drv\drv_mpu6050.c            0x00000000   Number         0  drv_mpu6050.o ABSOLUTE
    ..\HARDWARE\med\med_mcu.c                0x00000000   Number         0  med_mcu.o ABSOLUTE
    ..\HARDWARE\med\med_mpu6050.c            0x00000000   Number         0  med_mpu6050.o ABSOLUTE
    ..\HARDWARE\other\filter.c               0x00000000   Number         0  filter.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_bkp.c   0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_can.c   0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_cec.c   0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_crc.c   0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dac.c   0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dbgmcu.c 0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dma.c   0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_exti.c  0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_fsmc.c  0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_i2c.c   0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_iwdg.c  0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_pwr.c   0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rtc.c   0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_sdio.c  0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_spi.c   0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_tim.c   0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_wwdg.c  0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\exit\exit.c                    0x00000000   Number         0  exit.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001a4   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000017  0x080001aa   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001ae   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001b0   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080001b6   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001c0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001c0   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001c2   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080001c4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080001c4   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080001c6   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001c6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001c6   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001cc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001cc   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001d0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001d0   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001d8   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001da   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001da   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001de   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001e4   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000224   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08000228   Section        0  noretval__2printf.o(.text)
    .text                                    0x08000240   Section        0  __printf_wp.o(.text)
    .text                                    0x0800034e   Section        0  heapauxi.o(.text)
    .text                                    0x08000354   Section        2  use_no_semi.o(.text)
    .text                                    0x08000356   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000359   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000774   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000798   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080007a0   Section      138  lludiv10.o(.text)
    .text                                    0x0800082c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800082d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800085c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080008dc   Section        0  bigflt0.o(.text)
    .text                                    0x080009c0   Section        0  ferror.o(.text)
    .text                                    0x080009c8   Section        8  libspace.o(.text)
    .text                                    0x080009d0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000a1a   Section        0  exit.o(.text)
    .text                                    0x08000a2c   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08000aac   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08000aea   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08000b30   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08000b90   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08000ec8   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08000fa4   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08000fce   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08000ff8   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x0800123c   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08001240   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Drv_Mpu6050_Gpio_Init                  0x08001244   Section        0  drv_mpu6050.o(i.Drv_Mpu6050_Gpio_Init)
    i.EXTI9_5_IRQHandler                     0x0800128c   Section        0  exit.o(i.EXTI9_5_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x08001314   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x08001320   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x08001348   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.Exit_Init                              0x080013dc   Section        0  exit.o(i.Exit_Init)
    i.GPIO_EXTILineConfig                    0x0800142c   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x0800146c   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_SetBits                           0x08001582   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08001586   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Kalman_Filter_Angle                    0x0800158a   Section        0  filter.o(i.Kalman_Filter_Angle)
    i.MPU6050_CalibrateZeroPosition          0x080016c8   Section        0  med_mpu6050.o(i.MPU6050_CalibrateZeroPosition)
    i.MedMpu6050_SendOneByte                 0x080018bc   Section        0  med_mpu6050.o(i.MedMpu6050_SendOneByte)
    i.Med_Mcu_Iint                           0x0800191c   Section        0  med_mcu.o(i.Med_Mcu_Iint)
    i.Med_Mpu6050_GetAngles                  0x08001934   Section        0  med_mpu6050.o(i.Med_Mpu6050_GetAngles)
    i.Med_Mpu6050_Iic_SendAck                0x08001b44   Section        0  med_mpu6050.o(i.Med_Mpu6050_Iic_SendAck)
    i.Med_Mpu6050_Iic_SendNAck               0x08001b94   Section        0  med_mpu6050.o(i.Med_Mpu6050_Iic_SendNAck)
    i.Med_Mpu6050_Iic_Start                  0x08001be4   Section        0  med_mpu6050.o(i.Med_Mpu6050_Iic_Start)
    i.Med_Mpu6050_Iic_Stop                   0x08001c30   Section        0  med_mpu6050.o(i.Med_Mpu6050_Iic_Stop)
    i.Med_Mpu6050_Iic_WaitAck                0x08001c7c   Section        0  med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck)
    i.Med_Mpu6050_Init                       0x08001ce4   Section        0  med_mpu6050.o(i.Med_Mpu6050_Init)
    i.Med_Mpu6050_ReadOneByte                0x08001d58   Section        0  med_mpu6050.o(i.Med_Mpu6050_ReadOneByte)
    i.Med_Mpu6050_ReadReg                    0x08001dc8   Section        0  med_mpu6050.o(i.Med_Mpu6050_ReadReg)
    i.Med_Mpu6050_WriteReg                   0x08001e02   Section        0  med_mpu6050.o(i.Med_Mpu6050_WriteReg)
    i.MemManage_Handler                      0x08001e46   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001e4a   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001e4c   Section        0  misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x08001ebc   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB2PeriphClockCmd                 0x08001ec0   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001ee0   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08001fb4   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001fb6   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001fb7   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001fc0   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001fc1   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x080020a0   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x080020c8   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080020cc   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART_Cmd                              0x0800212c   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_Init                             0x08002144   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.UsageFault_Handler                     0x0800221c   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08002220   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x08002248   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x080022f2   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x080022f8   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_underflow                0x080022fc   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x0800230c   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x0800231a   Section        0  usart.o(i._sys_exit)
    i.atan                                   0x08002320   Section        0  atan.o(i.atan)
    i.atan2                                  0x08002540   Section        0  atan2.o(i.atan2)
    i.delay_init                             0x080026c0   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08002704   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08002750   Section        0  delay.o(i.delay_us)
    i.fputc                                  0x0800279c   Section        0  usart.o(i.fputc)
    i.main                                   0x080027b8   Section        0  main.o(i.main)
    i.uart_init                              0x080027c0   Section        0  usart.o(i.uart_init)
    locale$$code                             0x08002838   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$d2f                                0x08002864   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x080028c8   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080028d9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08002a18   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$ddiv                               0x08002a28   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08002a2f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dmul                               0x08002cd8   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08002e2c   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08002ec8   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08002ed4   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x08002eec   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08002efd   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x080030c0   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08003118   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08003127   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fdiv                               0x080031dc   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080031dd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$fflt                               0x08003360   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08003390   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fmul                               0x080033b8   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x080034ba   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08003546   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frsb                               0x08003550   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsub                               0x08003564   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08003573   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$printf1                            0x0800364e   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x08003652   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x080036b6   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x08003712   Section       48  trapv.o(x$fpl$trapveneer)
    x$fpl$usenofp                            0x08003742   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08003748   Section      152  atan.o(.constdata)
    atanhi                                   0x08003748   Data          32  atan.o(.constdata)
    atanlo                                   0x08003768   Data          32  atan.o(.constdata)
    aTodd                                    0x08003788   Data          40  atan.o(.constdata)
    aTeven                                   0x080037b0   Data          48  atan.o(.constdata)
    .constdata                               0x080037e0   Section        8  qnan.o(.constdata)
    .constdata                               0x080037e8   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080037e8   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08003824   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800389c   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080038a0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080038a8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080038b4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080038b6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080038b7   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x080038b8   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section        4  delay.o(.data)
    fac_us                                   0x20000014   Data           1  delay.o(.data)
    fac_ms                                   0x20000016   Data           2  delay.o(.data)
    .data                                    0x20000018   Section        4  usart.o(.data)
    .data                                    0x2000001c   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000001c   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x2000002c   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000030   Section        1  med_mpu6050.o(.data)
    firstRun                                 0x20000030   Data           1  med_mpu6050.o(.data)
    .data                                    0x20000034   Section      168  filter.o(.data)
    .bss                                     0x200000dc   Section       96  libspace.o(.bss)
    HEAP                                     0x20000140   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000140   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000340   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000340   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000740   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001a5   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x080001ab   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001af   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001c1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080001c3   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080001c5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080001c7   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001c7   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001cd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001cd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001d1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001d1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001d9   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001db   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001db   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001df   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001e5   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080001ff   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000201   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __use_no_semihosting                     0x08000225   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08000229   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x08000241   Thumb Code   270  __printf_wp.o(.text)
    __use_two_region_memory                  0x0800034f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000351   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000353   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000355   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000355   Thumb Code     2  use_no_semi.o(.text)
    __lib_sel_fp_printf                      0x08000357   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000509   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_file                        0x08000775   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x08000799   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x080007a1   Thumb Code   138  lludiv10.o(.text)
    _printf_char_common                      0x08000837   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x0800085d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080008dd   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x080009c1   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x080009c9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080009c9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080009c9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080009d1   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000a1b   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000a2d   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08000aad   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08000aeb   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08000b31   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08000b91   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08000ec9   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08000fa5   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08000fcf   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08000ff9   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x0800123d   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08001241   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Drv_Mpu6050_Gpio_Init                    0x08001245   Thumb Code    68  drv_mpu6050.o(i.Drv_Mpu6050_Gpio_Init)
    EXTI9_5_IRQHandler                       0x0800128d   Thumb Code    88  exit.o(i.EXTI9_5_IRQHandler)
    EXTI_ClearITPendingBit                   0x08001315   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x08001321   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x08001349   Thumb Code   142  stm32f10x_exti.o(i.EXTI_Init)
    Exit_Init                                0x080013dd   Thumb Code    78  exit.o(i.Exit_Init)
    GPIO_EXTILineConfig                      0x0800142d   Thumb Code    60  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x0800146d   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_SetBits                             0x08001583   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08001587   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Kalman_Filter_Angle                      0x0800158b   Thumb Code   316  filter.o(i.Kalman_Filter_Angle)
    MPU6050_CalibrateZeroPosition            0x080016c9   Thumb Code   462  med_mpu6050.o(i.MPU6050_CalibrateZeroPosition)
    MedMpu6050_SendOneByte                   0x080018bd   Thumb Code    84  med_mpu6050.o(i.MedMpu6050_SendOneByte)
    Med_Mcu_Iint                             0x0800191d   Thumb Code    24  med_mcu.o(i.Med_Mcu_Iint)
    Med_Mpu6050_GetAngles                    0x08001935   Thumb Code   484  med_mpu6050.o(i.Med_Mpu6050_GetAngles)
    Med_Mpu6050_Iic_SendAck                  0x08001b45   Thumb Code    66  med_mpu6050.o(i.Med_Mpu6050_Iic_SendAck)
    Med_Mpu6050_Iic_SendNAck                 0x08001b95   Thumb Code    66  med_mpu6050.o(i.Med_Mpu6050_Iic_SendNAck)
    Med_Mpu6050_Iic_Start                    0x08001be5   Thumb Code    64  med_mpu6050.o(i.Med_Mpu6050_Iic_Start)
    Med_Mpu6050_Iic_Stop                     0x08001c31   Thumb Code    62  med_mpu6050.o(i.Med_Mpu6050_Iic_Stop)
    Med_Mpu6050_Iic_WaitAck                  0x08001c7d   Thumb Code    90  med_mpu6050.o(i.Med_Mpu6050_Iic_WaitAck)
    Med_Mpu6050_Init                         0x08001ce5   Thumb Code   114  med_mpu6050.o(i.Med_Mpu6050_Init)
    Med_Mpu6050_ReadOneByte                  0x08001d59   Thumb Code    98  med_mpu6050.o(i.Med_Mpu6050_ReadOneByte)
    Med_Mpu6050_ReadReg                      0x08001dc9   Thumb Code    58  med_mpu6050.o(i.Med_Mpu6050_ReadReg)
    Med_Mpu6050_WriteReg                     0x08001e03   Thumb Code    68  med_mpu6050.o(i.Med_Mpu6050_WriteReg)
    MemManage_Handler                        0x08001e47   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001e4b   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001e4d   Thumb Code   100  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08001ebd   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB2PeriphClockCmd                   0x08001ec1   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001ee1   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08001fb5   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x080020a1   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x080020c9   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080020cd   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART_Cmd                                0x0800212d   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_Init                               0x08002145   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    UsageFault_Handler                       0x0800221d   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08002221   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x08002249   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x080022f3   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x080022f9   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_underflow                  0x080022fd   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x0800230d   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x0800231b   Thumb Code     4  usart.o(i._sys_exit)
    atan                                     0x08002321   Thumb Code   474  atan.o(i.atan)
    atan2                                    0x08002541   Thumb Code   346  atan2.o(i.atan2)
    delay_init                               0x080026c1   Thumb Code    50  delay.o(i.delay_init)
    delay_ms                                 0x08002705   Thumb Code    72  delay.o(i.delay_ms)
    delay_us                                 0x08002751   Thumb Code    72  delay.o(i.delay_us)
    fputc                                    0x0800279d   Thumb Code    24  usart.o(i.fputc)
    main                                     0x080027b9   Thumb Code     8  main.o(i.main)
    uart_init                                0x080027c1   Thumb Code   112  usart.o(i.uart_init)
    _get_lc_numeric                          0x08002839   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_d2f                              0x08002865   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08002865   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x080028c9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080028c9   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08002a19   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_ddiv                             0x08002a29   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002a29   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_dmul                             0x08002cd9   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08002cd9   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08002e2d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08002ec9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08002ed5   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08002ed5   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x08002eed   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08002eed   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x080030c1   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080030c1   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08003119   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08003119   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fdiv                             0x080031dd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080031dd   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_i2f                              0x08003361   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08003361   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08003391   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08003391   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_fmul                             0x080033b9   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080033b9   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x080034bb   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08003547   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_frsub                            0x08003551   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x08003551   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    __aeabi_fsub                             0x08003565   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08003565   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    _printf_fp_dec                           0x0800364f   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x08003653   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x080036b7   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x08003713   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08003742   Number         0  usenofp.o(x$fpl$usenofp)
    __mathlib_zero                           0x080037e0   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800387c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800389c   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x20000018   Data           4  usart.o(.data)
    gZeroAngleX                              0x20000034   Data           4  filter.o(.data)
    gZeroAngleY                              0x20000038   Data           4  filter.o(.data)
    gZeroAngleZ                              0x2000003c   Data           4  filter.o(.data)
    kfX                                      0x20000040   Data          52  filter.o(.data)
    kfY                                      0x20000074   Data          52  filter.o(.data)
    kfZ                                      0x200000a8   Data          52  filter.o(.data)
    __libspace_start                         0x200000dc   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000013c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001e5

  Load Region LR_1 (Base: 0x08000000, Size: 0x00003994, Max: 0xffffffff, ABSOLUTE)

    Execution Region ER_RO (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000038b8, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          362    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO         3512  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         3818    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO         3820    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO         3822    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         3509    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO         3508    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080001aa   0x080001aa   0x00000004   Code   RO         3567    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001ae   0x080001ae   0x00000002   Code   RO         3689    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3691    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3693    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3696    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3698    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         3700    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000006   Code   RO         3701    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         3703    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         3705    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         3707    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b6   0x080001b6   0x0000000a   Code   RO         3708    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3709    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3711    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3713    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3715    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3717    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3719    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3721    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3723    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3727    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3729    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3731    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3733    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001c0   0x080001c0   0x00000002   Code   RO         3734    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001c2   0x080001c2   0x00000002   Code   RO         3768    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3777    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3779    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3782    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3785    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3787    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         3790    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001c4   0x080001c4   0x00000002   Code   RO         3791    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         3560    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         3603    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001c6   0x080001c6   0x00000006   Code   RO         3615    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001cc   0x080001cc   0x00000000   Code   RO         3605    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001cc   0x080001cc   0x00000004   Code   RO         3606    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001d0   0x080001d0   0x00000000   Code   RO         3608    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001d0   0x080001d0   0x00000008   Code   RO         3609    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001d8   0x080001d8   0x00000002   Code   RO         3737    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001da   0x080001da   0x00000000   Code   RO         3746    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001da   0x080001da   0x00000004   Code   RO         3747    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001de   0x080001de   0x00000006   Code   RO         3748    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001e4   0x080001e4   0x00000040   Code   RO          363  * .text               startup_stm32f10x_hd.o
    0x08000224   0x08000224   0x00000002   Code   RO         3480    .text               c_w.l(use_no_semi_2.o)
    0x08000226   0x08000226   0x00000002   PAD
    0x08000228   0x08000228   0x00000018   Code   RO         3484    .text               c_w.l(noretval__2printf.o)
    0x08000240   0x08000240   0x0000010e   Code   RO         3496    .text               c_w.l(__printf_wp.o)
    0x0800034e   0x0800034e   0x00000006   Code   RO         3510    .text               c_w.l(heapauxi.o)
    0x08000354   0x08000354   0x00000002   Code   RO         3558    .text               c_w.l(use_no_semi.o)
    0x08000356   0x08000356   0x0000041e   Code   RO         3563    .text               c_w.l(_printf_fp_dec.o)
    0x08000774   0x08000774   0x00000024   Code   RO         3565    .text               c_w.l(_printf_char_file.o)
    0x08000798   0x08000798   0x00000008   Code   RO         3620    .text               c_w.l(rt_locale_intlibspace.o)
    0x080007a0   0x080007a0   0x0000008a   Code   RO         3627    .text               c_w.l(lludiv10.o)
    0x0800082a   0x0800082a   0x00000002   PAD
    0x0800082c   0x0800082c   0x00000030   Code   RO         3629    .text               c_w.l(_printf_char_common.o)
    0x0800085c   0x0800085c   0x00000080   Code   RO         3631    .text               c_w.l(_printf_fp_infnan.o)
    0x080008dc   0x080008dc   0x000000e4   Code   RO         3633    .text               c_w.l(bigflt0.o)
    0x080009c0   0x080009c0   0x00000008   Code   RO         3658    .text               c_w.l(ferror.o)
    0x080009c8   0x080009c8   0x00000008   Code   RO         3673    .text               c_w.l(libspace.o)
    0x080009d0   0x080009d0   0x0000004a   Code   RO         3676    .text               c_w.l(sys_stackheap_outer.o)
    0x08000a1a   0x08000a1a   0x00000012   Code   RO         3680    .text               c_w.l(exit.o)
    0x08000a2c   0x08000a2c   0x00000080   Code   RO         3682    .text               c_w.l(strcmpv7m.o)
    0x08000aac   0x08000aac   0x0000003e   Code   RO         3636    CL$$btod_d2e        c_w.l(btod.o)
    0x08000aea   0x08000aea   0x00000046   Code   RO         3638    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08000b30   0x08000b30   0x00000060   Code   RO         3637    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08000b90   0x08000b90   0x00000338   Code   RO         3646    CL$$btod_div_common  c_w.l(btod.o)
    0x08000ec8   0x08000ec8   0x000000dc   Code   RO         3643    CL$$btod_e2e        c_w.l(btod.o)
    0x08000fa4   0x08000fa4   0x0000002a   Code   RO         3640    CL$$btod_ediv       c_w.l(btod.o)
    0x08000fce   0x08000fce   0x0000002a   Code   RO         3639    CL$$btod_emul       c_w.l(btod.o)
    0x08000ff8   0x08000ff8   0x00000244   Code   RO         3645    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800123c   0x0800123c   0x00000004   Code   RO          107    i.BusFault_Handler  stm32f10x_it.o
    0x08001240   0x08001240   0x00000002   Code   RO          108    i.DebugMon_Handler  stm32f10x_it.o
    0x08001242   0x08001242   0x00000002   PAD
    0x08001244   0x08001244   0x00000048   Code   RO         3334    i.Drv_Mpu6050_Gpio_Init  drv_mpu6050.o
    0x0800128c   0x0800128c   0x00000088   Code   RO          322    i.EXTI9_5_IRQHandler  exit.o
    0x08001314   0x08001314   0x0000000c   Code   RO         1659    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08001320   0x08001320   0x00000028   Code   RO         1663    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x08001348   0x08001348   0x00000094   Code   RO         1664    i.EXTI_Init         stm32f10x_exti.o
    0x080013dc   0x080013dc   0x0000004e   Code   RO          323    i.Exit_Init         exit.o
    0x0800142a   0x0800142a   0x00000002   PAD
    0x0800142c   0x0800142c   0x00000040   Code   RO          406    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x0800146c   0x0800146c   0x00000116   Code   RO          409    i.GPIO_Init         stm32f10x_gpio.o
    0x08001582   0x08001582   0x00000004   Code   RO          417    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08001586   0x08001586   0x00000004   Code   RO          109    i.HardFault_Handler  stm32f10x_it.o
    0x0800158a   0x0800158a   0x0000013c   Code   RO         3460    i.Kalman_Filter_Angle  filter.o
    0x080016c6   0x080016c6   0x00000002   PAD
    0x080016c8   0x080016c8   0x000001f4   Code   RO         3361    i.MPU6050_CalibrateZeroPosition  med_mpu6050.o
    0x080018bc   0x080018bc   0x00000060   Code   RO         3362    i.MedMpu6050_SendOneByte  med_mpu6050.o
    0x0800191c   0x0800191c   0x00000018   Code   RO         3349    i.Med_Mcu_Iint      med_mcu.o
    0x08001934   0x08001934   0x00000210   Code   RO         3363    i.Med_Mpu6050_GetAngles  med_mpu6050.o
    0x08001b44   0x08001b44   0x00000050   Code   RO         3364    i.Med_Mpu6050_Iic_SendAck  med_mpu6050.o
    0x08001b94   0x08001b94   0x00000050   Code   RO         3365    i.Med_Mpu6050_Iic_SendNAck  med_mpu6050.o
    0x08001be4   0x08001be4   0x0000004c   Code   RO         3366    i.Med_Mpu6050_Iic_Start  med_mpu6050.o
    0x08001c30   0x08001c30   0x0000004c   Code   RO         3367    i.Med_Mpu6050_Iic_Stop  med_mpu6050.o
    0x08001c7c   0x08001c7c   0x00000068   Code   RO         3368    i.Med_Mpu6050_Iic_WaitAck  med_mpu6050.o
    0x08001ce4   0x08001ce4   0x00000072   Code   RO         3369    i.Med_Mpu6050_Init  med_mpu6050.o
    0x08001d56   0x08001d56   0x00000002   PAD
    0x08001d58   0x08001d58   0x00000070   Code   RO         3371    i.Med_Mpu6050_ReadOneByte  med_mpu6050.o
    0x08001dc8   0x08001dc8   0x0000003a   Code   RO         3372    i.Med_Mpu6050_ReadReg  med_mpu6050.o
    0x08001e02   0x08001e02   0x00000044   Code   RO         3374    i.Med_Mpu6050_WriteReg  med_mpu6050.o
    0x08001e46   0x08001e46   0x00000004   Code   RO          110    i.MemManage_Handler  stm32f10x_it.o
    0x08001e4a   0x08001e4a   0x00000002   Code   RO          111    i.NMI_Handler       stm32f10x_it.o
    0x08001e4c   0x08001e4c   0x00000070   Code   RO          367    i.NVIC_Init         misc.o
    0x08001ebc   0x08001ebc   0x00000002   Code   RO          112    i.PendSV_Handler    stm32f10x_it.o
    0x08001ebe   0x08001ebe   0x00000002   PAD
    0x08001ec0   0x08001ec0   0x00000020   Code   RO          545    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001ee0   0x08001ee0   0x000000d4   Code   RO          553    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08001fb4   0x08001fb4   0x00000002   Code   RO          113    i.SVC_Handler       stm32f10x_it.o
    0x08001fb6   0x08001fb6   0x00000008   Code   RO          196    i.SetSysClock       system_stm32f10x.o
    0x08001fbe   0x08001fbe   0x00000002   PAD
    0x08001fc0   0x08001fc0   0x000000e0   Code   RO          197    i.SetSysClockTo72   system_stm32f10x.o
    0x080020a0   0x080020a0   0x00000028   Code   RO          371    i.SysTick_CLKSourceConfig  misc.o
    0x080020c8   0x080020c8   0x00000002   Code   RO          114    i.SysTick_Handler   stm32f10x_it.o
    0x080020ca   0x080020ca   0x00000002   PAD
    0x080020cc   0x080020cc   0x00000060   Code   RO          199    i.SystemInit        system_stm32f10x.o
    0x0800212c   0x0800212c   0x00000018   Code   RO          745    i.USART_Cmd         stm32f10x_usart.o
    0x08002144   0x08002144   0x000000d8   Code   RO          752    i.USART_Init        stm32f10x_usart.o
    0x0800221c   0x0800221c   0x00000004   Code   RO          115    i.UsageFault_Handler  stm32f10x_it.o
    0x08002220   0x08002220   0x00000028   Code   RO         3669    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08002248   0x08002248   0x000000aa   Code   RO         3671    i.__kernel_poly     m_ws.l(poly.o)
    0x080022f2   0x080022f2   0x00000006   Code   RO         3588    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x080022f8   0x080022f8   0x00000004   Code   RO         3589    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x080022fc   0x080022fc   0x00000010   Code   RO         3593    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x0800230c   0x0800230c   0x0000000e   Code   RO         3498    i._is_digit         c_w.l(__printf_wp.o)
    0x0800231a   0x0800231a   0x00000004   Code   RO          288    i._sys_exit         usart.o
    0x0800231e   0x0800231e   0x00000002   PAD
    0x08002320   0x08002320   0x00000220   Code   RO         3578    i.atan              m_ws.l(atan.o)
    0x08002540   0x08002540   0x00000180   Code   RO         3551    i.atan2             m_ws.l(atan2.o)
    0x080026c0   0x080026c0   0x00000044   Code   RO          230    i.delay_init        delay.o
    0x08002704   0x08002704   0x0000004c   Code   RO          231    i.delay_ms          delay.o
    0x08002750   0x08002750   0x0000004c   Code   RO          232    i.delay_us          delay.o
    0x0800279c   0x0800279c   0x0000001c   Code   RO          289    i.fputc             usart.o
    0x080027b8   0x080027b8   0x00000008   Code   RO            1    i.main              main.o
    0x080027c0   0x080027c0   0x00000078   Code   RO          290    i.uart_init         usart.o
    0x08002838   0x08002838   0x0000002c   Code   RO         3663    locale$$code        c_w.l(lc_numeric_c.o)
    0x08002864   0x08002864   0x00000062   Code   RO         3514    x$fpl$d2f           fz_ws.l(d2f.o)
    0x080028c6   0x080028c6   0x00000002   PAD
    0x080028c8   0x080028c8   0x00000150   Code   RO         3516    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08002a18   0x08002a18   0x00000010   Code   RO         3735    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08002a28   0x08002a28   0x000002b0   Code   RO         3523    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08002cd8   0x08002cd8   0x00000154   Code   RO         3526    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08002e2c   0x08002e2c   0x0000009c   Code   RO         3568    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08002ec8   0x08002ec8   0x0000000c   Code   RO         3570    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08002ed4   0x08002ed4   0x00000016   Code   RO         3517    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x08002eea   0x08002eea   0x00000002   PAD
    0x08002eec   0x08002eec   0x000001d4   Code   RO         3518    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x080030c0   0x080030c0   0x00000056   Code   RO         3528    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08003116   0x08003116   0x00000002   PAD
    0x08003118   0x08003118   0x000000c4   Code   RO         3530    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080031dc   0x080031dc   0x00000184   Code   RO         3537    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08003360   0x08003360   0x00000030   Code   RO         3541    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08003390   0x08003390   0x00000026   Code   RO         3540    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x080033b6   0x080033b6   0x00000002   PAD
    0x080033b8   0x080033b8   0x00000102   Code   RO         3546    x$fpl$fmul          fz_ws.l(fmul.o)
    0x080034ba   0x080034ba   0x0000008c   Code   RO         3572    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08003546   0x08003546   0x0000000a   Code   RO         3574    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08003550   0x08003550   0x00000014   Code   RO         3531    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x08003564   0x08003564   0x000000ea   Code   RO         3532    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x0800364e   0x0800364e   0x00000004   Code   RO         3548    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08003652   0x08003652   0x00000064   Code   RO         3743    x$fpl$retnan        fz_ws.l(retnan.o)
    0x080036b6   0x080036b6   0x0000005c   Code   RO         3667    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x08003712   0x08003712   0x00000030   Code   RO         3756    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08003742   0x08003742   0x00000000   Code   RO         3576    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08003742   0x08003742   0x00000006   PAD
    0x08003748   0x08003748   0x00000098   Data   RO         3579    .constdata          m_ws.l(atan.o)
    0x080037e0   0x080037e0   0x00000008   Data   RO         3601    .constdata          m_ws.l(qnan.o)
    0x080037e8   0x080037e8   0x00000094   Data   RO         3634    .constdata          c_w.l(bigflt0.o)
    0x0800387c   0x0800387c   0x00000020   Data   RO         3816    Region$$Table       anon$$obj.o
    0x0800389c   0x0800389c   0x0000001c   Data   RO         3662    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region ER_RW (Exec base: 0x20000000, Load base: 0x080038b8, Size: 0x000000dc, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080038b8   0x00000014   Data   RW          200    .data               system_stm32f10x.o
    0x20000014   0x080038cc   0x00000004   Data   RW          233    .data               delay.o
    0x20000018   0x080038d0   0x00000004   Data   RW          291    .data               usart.o
    0x2000001c   0x080038d4   0x00000014   Data   RW          573    .data               stm32f10x_rcc.o
    0x20000030   0x080038e8   0x00000001   Data   RW         3375    .data               med_mpu6050.o
    0x20000031   0x080038e9   0x00000003   PAD
    0x20000034   0x080038ec   0x000000a8   Data   RW         3461    .data               filter.o


    Execution Region ER_ZI (Exec base: 0x200000dc, Load base: 0x08003994, Size: 0x00000664, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x200000dc        -       0x00000060   Zero   RW         3674    .bss                c_w.l(libspace.o)
    0x2000013c   0x08003994   0x00000004   PAD
    0x20000140        -       0x00000200   Zero   RW          361    HEAP                startup_stm32f10x_hd.o
    0x20000340        -       0x00000400   Zero   RW          360    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       220         26          0          4          0       1851   delay.o
        72          4          0          0          0        576   drv_mpu6050.o
       214         48          0          0          0       1177   exit.o
       316          0          0        168          0       2200   filter.o
         8          0          0          0          0     233915   main.o
        24          0          0          0          0        503   med_mcu.o
      1892        176          0          1          0       7969   med_mpu6050.o
       152         12          0          0          0       1642   misc.o
        64         26        304          0       1536        820   startup_stm32f10x_hd.o
       200         18          0          0          0       2240   stm32f10x_exti.o
       346          4          0          0          0       3245   stm32f10x_gpio.o
        26          0          0          0          0       4002   stm32f10x_it.o
       244         26          0         20          0       4001   stm32f10x_rcc.o
       240          6          0          0          0       2945   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       328         28          0         20          0       2257   system_stm32f10x.o
       152         12          0          4          0       3586   usart.o

    ----------------------------------------------------------------------
      4514        <USER>        <GROUP>        220       1536     272961   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          0          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        98          4          0          0          0         92   d2f.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
       688        140          0          0          0        208   ddiv.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       450          8          0          0          0        236   faddsub_clz.o
       388         76          0          0          0         96   fdiv.o
        86          0          0          0          0        136   fflt_clz.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       544         70        152          0          0        124   atan.o
       384         38          0          0          0        144   atan2.o
        26          6          0          0          0        204   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
         0          0          8          0          0          0   qnan.o

    ----------------------------------------------------------------------
      9334        <USER>        <GROUP>          0        100       5104   Library Totals
        20          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4352        188        176          0         96       2428   c_w.l
      3798        276          0          0          0       2040   fz_ws.l
      1164        114        160          0          0        636   m_ws.l

    ----------------------------------------------------------------------
      9334        <USER>        <GROUP>          0        100       5104   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13848        964        672        220       1636     272965   Grand Totals
     13848        964        672        220       1636     272965   ELF Image Totals
     13848        964        672        220          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                14520 (  14.18kB)
    Total RW  Size (RW Data + ZI Data)              1856 (   1.81kB)
    Total ROM Size (Code + RO Data + RW Data)      14740 (  14.39kB)

==============================================================================

