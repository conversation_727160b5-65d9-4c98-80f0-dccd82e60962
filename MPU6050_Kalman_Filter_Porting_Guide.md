# MPU6050卡尔曼滤波移植指南 - STM32 F1 HAL库

## 概述

本指南将帮助您将当前基于标准库的MPU6050卡尔曼滤波代码移植到STM32 F1 HAL库环境中。移植后将保持相同的功能：三轴角度获取、卡尔曼滤波处理、零位校准等。

## 原工程分析

### 文件结构
```
原工程/
├── HARDWARE/other/filter.c/h          # 卡尔曼滤波算法
├── HARDWARE/med/med_mpu6050.c/h       # MPU6050协议层
├── HARDWARE/drv/drv_mpu6050.c/h       # GPIO硬件层
└── SYSTEM/delay/delay.c/h             # 延时函数
```

### 核心功能模块
1. **卡尔曼滤波算法** - 纯数学运算，可直接移植
2. **IIC软件模拟** - 需要适配HAL库的GPIO操作
3. **MPU6050寄存器操作** - 协议层，逻辑保持不变
4. **延时函数** - 需要适配HAL库的延时函数

## 移植步骤

### 第一步：创建新的HAL库工程

1. 使用STM32CubeMX创建新工程
2. 选择对应的STM32F1系列芯片
3. 配置系统时钟
4. 配置GPIO引脚（建议使用PB6作为SCL，PB7作为SDA）
5. 生成HAL库代码

### 第二步：移植卡尔曼滤波算法

创建文件：`mpu6050_filter.h`
```c
#ifndef __MPU6050_FILTER_H
#define __MPU6050_FILTER_H

#include "main.h"
#include <math.h>

#define PI 3.14159265f

typedef struct {
    float angle;      // 滤波后的角度
    float Q_bias;     // 陀螺仪偏差
    float P[2][2];    // 协方差矩阵
    float K1, K2;     // 卡尔曼增益
    float dt;         // 采样时间间隔
    float Q_angle;    // 角度过程噪声
    float Q_gyro;     // 陀螺仪过程噪声  
    float R_angle;    // 角度测量噪声
} KalmanFilter_t;

// 全局变量声明
extern float gZeroAngleX, gZeroAngleY, gZeroAngleZ;
extern KalmanFilter_t kfX, kfY, kfZ;

// 函数声明
float Kalman_Filter_Angle(float AccelAngle, float GyroRate, KalmanFilter_t *kf);
void Kalman_Filter_Init(void);

#endif
```

创建文件：`mpu6050_filter.c`
```c
#include "mpu6050_filter.h"

// 零位角度
float gZeroAngleX = 0;
float gZeroAngleY = 0; 
float gZeroAngleZ = 0;

// 三轴卡尔曼滤波器
KalmanFilter_t kfX = {0, 0, {{1, 0}, {0, 1}}, 0, 0, 0.005f, 0.01f, 0.003f, 0.03f};
KalmanFilter_t kfY = {0, 0, {{1, 0}, {0, 1}}, 0, 0, 0.005f, 0.01f, 0.003f, 0.03f};
KalmanFilter_t kfZ = {0, 0, {{1, 0}, {0, 1}}, 0, 0, 0.005f, 0.01f, 0.003f, 0.03f};

void Kalman_Filter_Init(void)
{
    // 初始化三个滤波器
    kfX.angle = 0; kfX.Q_bias = 0;
    kfY.angle = 0; kfY.Q_bias = 0;
    kfZ.angle = 0; kfZ.Q_bias = 0;
}

float Kalman_Filter_Angle(float AccelAngle, float GyroRate, KalmanFilter_t *kf)
{
    // 预测步骤
    kf->angle += (GyroRate - kf->Q_bias) * kf->dt;
    
    // 更新协方差矩阵
    float Pdot[4] = {
        kf->Q_angle - kf->P[0][1] - kf->P[1][0],
        -kf->P[1][1],
        -kf->P[1][1], 
        kf->Q_gyro
    };
    
    kf->P[0][0] += Pdot[0] * kf->dt;
    kf->P[0][1] += Pdot[1] * kf->dt;
    kf->P[1][0] += Pdot[2] * kf->dt;
    kf->P[1][1] += Pdot[3] * kf->dt;
    
    // 计算卡尔曼增益
    float Angle_err = AccelAngle - kf->angle;
    float PCt_0 = kf->P[0][0];
    float PCt_1 = kf->P[1][0];
    float E = kf->R_angle + PCt_0;
    
    kf->K1 = PCt_0 / E;
    kf->K2 = PCt_1 / E;
    
    // 更新协方差矩阵
    float t_0 = PCt_0;
    float t_1 = kf->P[0][1];
    
    kf->P[0][0] -= kf->K1 * t_0;
    kf->P[0][1] -= kf->K1 * t_1;
    kf->P[1][0] -= kf->K2 * t_0;
    kf->P[1][1] -= kf->K2 * t_1;
    
    // 更新状态
    kf->angle += kf->K1 * Angle_err;
    kf->Q_bias += kf->K2 * Angle_err;
    
    return kf->angle;
}
```

### 第三步：适配GPIO操作

创建文件：`mpu6050_hal.h`
```c
#ifndef __MPU6050_HAL_H
#define __MPU6050_HAL_H

#include "main.h"

// GPIO引脚定义（根据实际连接修改）
#define MPU6050_SCL_GPIO_Port GPIOB
#define MPU6050_SCL_Pin GPIO_PIN_6
#define MPU6050_SDA_GPIO_Port GPIOB  
#define MPU6050_SDA_Pin GPIO_PIN_7

// GPIO操作宏
#define MPU6050_SCL_HIGH() HAL_GPIO_WritePin(MPU6050_SCL_GPIO_Port, MPU6050_SCL_Pin, GPIO_PIN_SET)
#define MPU6050_SCL_LOW()  HAL_GPIO_WritePin(MPU6050_SCL_GPIO_Port, MPU6050_SCL_Pin, GPIO_PIN_RESET)
#define MPU6050_SDA_HIGH() HAL_GPIO_WritePin(MPU6050_SDA_GPIO_Port, MPU6050_SDA_Pin, GPIO_PIN_SET)
#define MPU6050_SDA_LOW()  HAL_GPIO_WritePin(MPU6050_SDA_GPIO_Port, MPU6050_SDA_Pin, GPIO_PIN_RESET)
#define MPU6050_SDA_READ() HAL_GPIO_ReadPin(MPU6050_SDA_GPIO_Port, MPU6050_SDA_Pin)

// SDA方向控制
void MPU6050_SDA_OUT(void);
void MPU6050_SDA_IN(void);

// 延时函数
#define MPU6050_Delay_us(us) HAL_Delay_us(us)  // 需要实现微秒延时
#define MPU6050_Delay_ms(ms) HAL_Delay(ms)

// 初始化函数
void MPU6050_GPIO_Init(void);

#endif
```

### 第四步：实现微秒延时函数

在`main.c`中添加微秒延时函数：
```c
void HAL_Delay_us(uint32_t us)
{
    uint32_t start = __HAL_TIM_GET_COUNTER(&htim1);  // 假设使用TIM1
    while((__HAL_TIM_GET_COUNTER(&htim1) - start) < us);
}
```

注意：需要在CubeMX中配置一个定时器作为微秒计时器。

### 第五步：移植IIC通信协议

创建文件：`mpu6050_driver.h`
```c
#ifndef __MPU6050_DRIVER_H
#define __MPU6050_DRIVER_H

#include "mpu6050_hal.h"
#include "mpu6050_filter.h"

// MPU6050设备地址
#define MPU6050_ADDR 0x68

// 寄存器地址定义
#define MPU_PWR_MGMT1_REG    0x6B
#define MPU_SAMPLE_RATE_REG  0x19
#define MPU_CFG_REG          0x1A
#define MPU_GYRO_CFG_REG     0x1B
#define MPU_ACCEL_CFG_REG    0x1C
#define MPU_ACCEL_XOUTH_REG  0x3B
#define MPU_GYRO_XOUTH_REG   0x43

// 函数声明
uint8_t MPU6050_Init(void);
void MPU6050_GetAngles(float *angleX, float *angleY, float *angleZ);
void MPU6050_Calibrate(void);
uint8_t MPU6050_ReadReg(uint8_t reg);
void MPU6050_WriteReg(uint8_t reg, uint8_t data);

#endif
```

## 配置说明

### CubeMX配置要点

1. **GPIO配置**
   - PB6: GPIO_Output (SCL)
   - PB7: GPIO_Output (SDA，需要支持输入输出切换)

2. **定时器配置**
   - 配置一个定时器用于微秒延时
   - 时钟频率设置为1MHz，便于微秒计时

3. **串口配置**（可选）
   - 用于调试输出角度数据

### 使用示例

在`main.c`中的使用方法：
```c
int main(void)
{
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_USART1_UART_Init();
    
    // 初始化MPU6050
    if(MPU6050_Init() == 0)
    {
        printf("MPU6050初始化成功\r\n");
        MPU6050_Calibrate();  // 零位校准
    }
    
    float angleX, angleY, angleZ;
    
    while (1)
    {
        MPU6050_GetAngles(&angleX, &angleY, &angleZ);
        printf("X:%.1f Y:%.1f Z:%.1f\r\n", angleX, angleY, angleZ);
        HAL_Delay(100);
    }
}
```

## 注意事项

1. **时序要求**：IIC软件模拟对时序要求严格，确保延时函数准确
2. **GPIO配置**：SDA引脚需要支持输入输出方向切换
3. **中断处理**：如果使用外部中断触发数据读取，需要配置EXTI
4. **浮点运算**：确保编译器支持浮点运算，可能需要链接数学库

## 常见问题

1. **通信失败**：检查GPIO配置和延时时序
2. **数据异常**：检查寄存器配置和数据转换
3. **滤波效果差**：调整卡尔曼滤波参数

完成以上步骤后，您就可以在HAL库环境中使用MPU6050卡尔曼滤波功能了。
