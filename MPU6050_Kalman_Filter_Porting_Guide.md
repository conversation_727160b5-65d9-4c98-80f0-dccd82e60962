# MPU6050卡尔曼滤波移植指南 - STM32 F1 HAL库

## 概述

本指南将帮助您将当前基于标准库的MPU6050卡尔曼滤波代码移植到STM32 F1 HAL库环境中。您只需要复制粘贴原有的.c和.h文件，然后按照本指南进行简单的修改即可。

## 文件复制和修改指南

### 第一步：复制文件到新工程

将以下文件复制到您的HAL库工程中：

1. **复制 `HARDWARE/other/filter.c` 和 `filter.h`**
   - 卡尔曼滤波算法，无需修改，直接使用

2. **复制 `HARDWARE/med/med_mpu6050.c` 和 `med_mpu6050.h`**
   - MPU6050协议层，需要修改头文件包含

3. **复制 `HARDWARE/drv/drv_mpu6050.c` 和 `drv_mpu6050.h`**
   - GPIO硬件层，需要适配HAL库

### 第二步：修改头文件包含

#### 修改 `filter.h`
```c
// 原来的包含
// #include "sys.h"

// 改为HAL库包含
#include "main.h"
#include <math.h>
```

#### 修改 `med_mpu6050.h`
```c
// 原来的包含
// #include "sys.h"

// 改为HAL库包含
#include "main.h"
```

#### 修改 `med_mpu6050.c`
```c
// 原来的包含
// #include "delay.h"
// #include "drv_mpu6050.h"

// 改为
#include "filter.h"
// 延时函数直接使用HAL_Delay()和自定义的微秒延时
```

### 第三步：修改GPIO操作宏定义

#### 修改 `med_mpu6050.h` 中的GPIO宏定义

将原来的GPIO操作宏：
```c
// 原来的定义
#define MPU_IIC_SCL    PBout(6)
#define MPU_IIC_SDA    PBout(7)
#define MPU_READ_SDA   PBin(7)
#define MPU_SDA_IN()  {GPIOB->CRL&=0X0FFFFFFF;GPIOB->CRL|=(u32)8<<28;}
#define MPU_SDA_OUT() {GPIOB->CRL&=0X0FFFFFFF;GPIOB->CRL|=(u32)3<<28;}
```

改为HAL库的GPIO操作：
```c
// HAL库GPIO定义（根据您的实际连接修改引脚）
#define MPU_IIC_SCL_HIGH()  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET)
#define MPU_IIC_SCL_LOW()   HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET)
#define MPU_IIC_SDA_HIGH()  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET)
#define MPU_IIC_SDA_LOW()   HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET)
#define MPU_READ_SDA()      HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_7)

// SDA方向控制函数声明
void MPU_SDA_IN(void);
void MPU_SDA_OUT(void);
```

### 第四步：修改GPIO操作函数

#### 在 `med_mpu6050.c` 中修改所有GPIO操作

1. **修改IIC操作函数**：
```c
// 原来的 MPU_IIC_SCL = 1; 改为：
MPU_IIC_SCL_HIGH();

// 原来的 MPU_IIC_SCL = 0; 改为：
MPU_IIC_SCL_LOW();

// 原来的 MPU_IIC_SDA = 1; 改为：
MPU_IIC_SDA_HIGH();

// 原来的 MPU_IIC_SDA = 0; 改为：
MPU_IIC_SDA_LOW();

// 原来的 MPU_READ_SDA 改为：
MPU_READ_SDA()
```

2. **添加SDA方向控制函数**：
```c
void MPU_SDA_IN(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

void MPU_SDA_OUT(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}
```

### 第五步：修改延时函数

#### 替换延时函数调用

在 `med_mpu6050.c` 中：
```c
// 原来的 delay_us(2); 改为：
HAL_Delay_us(2);  // 需要自己实现微秒延时

// 原来的 delay_ms(10); 改为：
HAL_Delay(10);
```

#### 实现微秒延时函数

在 `main.c` 中添加：
```c
// 需要先在CubeMX中配置一个定时器（如TIM2）用于微秒计时
void HAL_Delay_us(uint32_t us)
{
    __HAL_TIM_SET_COUNTER(&htim2, 0);
    while(__HAL_TIM_GET_COUNTER(&htim2) < us);
}
```

### 第六步：修改drv_mpu6050.c

#### 适配GPIO初始化函数

将 `drv_mpu6050.c` 中的GPIO初始化改为HAL库方式：
```c
void Drv_Mpu6050_Gpio_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 使能GPIOB时钟（通常在CubeMX生成的代码中已经使能）
    __HAL_RCC_GPIOB_CLK_ENABLE();

    // 配置SCL引脚（PB6）
    GPIO_InitStruct.Pin = GPIO_PIN_6;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 配置SDA引脚（PB7）- 初始为输出模式
    GPIO_InitStruct.Pin = GPIO_PIN_7;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 设置初始电平为高
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6 | GPIO_PIN_7, GPIO_PIN_SET);
}
```

### 第七步：CubeMX配置

1. **GPIO配置**：
   - PB6: 不需要在CubeMX中特别配置，代码中动态配置
   - PB7: 不需要在CubeMX中特别配置，代码中动态配置

2. **定时器配置**：
   - 配置TIM2用于微秒延时
   - 预分频器设置为系统时钟频率-1（如72MHz系统，设置为71）
   - 计数器周期设置为0xFFFF

3. **串口配置**（可选）：
   - 用于调试输出

### 第八步：使用方法

在 `main.c` 中：
```c
#include "med_mpu6050.h"
#include "filter.h"

int main(void)
{
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_TIM2_Init();  // 微秒延时定时器
    MX_USART1_UART_Init();  // 可选

    // 启动定时器
    HAL_TIM_Base_Start(&htim2);

    // 初始化MPU6050
    Med_Mpu6050_Init();

    float angleX, angleY, angleZ;

    while (1)
    {
        Med_Mpu6050_GetAngles(&angleX, &angleY, &angleZ);
        printf("X:%.1f Y:%.1f Z:%.1f\r\n", angleX, angleY, angleZ);
        HAL_Delay(100);
    }
}
```

## 总结

通过以上修改，您就可以在HAL库环境中使用原有的MPU6050卡尔曼滤波代码了。主要修改点：

1. 头文件包含改为HAL库
2. GPIO操作宏改为HAL库函数
3. 延时函数改为HAL库函数
4. 添加SDA方向控制函数
5. 实现微秒延时函数

所有的算法逻辑和通信协议都保持不变，只是底层的硬件操作适配到HAL库。
