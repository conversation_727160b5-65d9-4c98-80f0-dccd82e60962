#ifndef __FILTER_H
#define __FILTER_H

#define PI   3.14159265

typedef struct {
    float angle;
    float Q_bias;
    float P[2][2];
    float K1, K2;
    float dt;
    float Q_angle;
    float Q_gyro;
    float R_angle;
    float offset;
} KalmanFilter;

extern float gZeroAngleX, gZeroAngleY, gZeroAngleZ;

extern KalmanFilter kfX;
extern KalmanFilter kfY;
extern KalmanFilter kfZ;

float Kalman_Filter_Angle(float AccelAngle, float GyroRate, KalmanFilter *kf);

#endif
